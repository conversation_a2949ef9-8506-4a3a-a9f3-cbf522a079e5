{"name": "ecoutetcf", "version": "2.0.0", "description": "", "type": "module", "main": "index.js", "scripts": {"build": "vite build", "start": "vite preview", "serve": "vercel dev", "dev": "vite", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "db:generate": "drizzle-kit generate", "db:migrate": "tsx db/migrations/migrate.ts", "db:migrate:prod": "tsx db/migrations/migrate.prod.ts", "db:reset:dev": "tsx db/scripts/reset-dev.ts", "db:import": "tsx db/scripts/import-data.ts", "db:import-single": "tsx db/scripts/import-single.ts", "db:test-import": "tsx db/scripts/test-import.ts", "db:test-performance": "tsx db/scripts/test-performance.ts", "db:copy-to-prod": "tsx db/scripts/copy-dev-to-prod.ts", "db:set-admin": "tsx db/scripts/set-admin.ts", "db:show-rate-limits": "tsx db/scripts/show-rate-limits.ts", "db:check-sequences": "tsx db/scripts/check-sequences.ts", "db:fix-sequences": "tsx db/scripts/fix-sequences.ts", "db:studio": "drizzle-kit studio"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "@vercel/analytics": "^1.5.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.0", "googleapis": "^149.0.0", "pg": "^8.16.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.1"}, "devDependencies": {"@eslint/js": "^9.27.0", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "@types/pg": "^8.15.2", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vercel/node": "^5.2.0", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.31.1", "eslint": "^9.27.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0", "vercel": "^42.2.0", "vite": "^6.3.5"}}