#!/usr/bin/env tsx

/**
 * Fix PostgreSQL sequences that are out of sync
 * This script resets all sequences to the correct values based on existing data
 */

import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { sql } from 'drizzle-orm';

// Load environment variables
config();

// Get database URL from environment
const databaseUrl = process.env.DATABASE_URL_PROD || process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ DATABASE_URL_PROD or DATABASE_URL environment variable is required');
  process.exit(1);
}

const pool = new Pool({
  connectionString: databaseUrl,
  ssl: databaseUrl.includes('localhost') ? false : { rejectUnauthorized: false }
});

const db = drizzle(pool);

async function fixSequences() {
  console.log('🔧 Fixing PostgreSQL sequences...');

  try {
    // Fix users sequence
    await db.execute(sql`
      SELECT setval('users_id_seq', COALESCE((SELECT MAX(id) FROM users), 1), true);
    `);
    console.log('✅ Fixed users_id_seq');

    // Fix questions sequence
    await db.execute(sql`
      SELECT setval('questions_id_seq', COALESCE((SELECT MAX(id) FROM questions), 1), true);
    `);
    console.log('✅ Fixed questions_id_seq');

    // Fix tests sequence
    await db.execute(sql`
      SELECT setval('tests_id_seq', COALESCE((SELECT MAX(id) FROM tests), 1), true);
    `);
    console.log('✅ Fixed tests_id_seq');

    // Fix test_questions sequence
    await db.execute(sql`
      SELECT setval('test_questions_id_seq', COALESCE((SELECT MAX(id) FROM test_questions), 1), true);
    `);
    console.log('✅ Fixed test_questions_id_seq');

    // Fix test_attempts sequence
    await db.execute(sql`
      SELECT setval('test_attempts_id_seq', COALESCE((SELECT MAX(id) FROM test_attempts), 1), true);
    `);
    console.log('✅ Fixed test_attempts_id_seq');

    // Fix test_attempt_answers sequence (this is the problematic one)
    await db.execute(sql`
      SELECT setval('test_attempt_answers_id_seq', COALESCE((SELECT MAX(id) FROM test_attempt_answers), 1), true);
    `);
    console.log('✅ Fixed test_attempt_answers_id_seq');

    // Fix rate_limits sequence
    await db.execute(sql`
      SELECT setval('rate_limits_id_seq', COALESCE((SELECT MAX(id) FROM rate_limits), 1), true);
    `);
    console.log('✅ Fixed rate_limits_id_seq');

    // Fix abuse_logs sequence
    await db.execute(sql`
      SELECT setval('abuse_logs_id_seq', COALESCE((SELECT MAX(id) FROM abuse_logs), 1), true);
    `);
    console.log('✅ Fixed abuse_logs_id_seq');

    console.log('🎉 All sequences have been fixed!');

    // Show current sequence values for verification
    console.log('\n📊 Current sequence values:');
    
    const sequences = [
      'users_id_seq',
      'questions_id_seq', 
      'tests_id_seq',
      'test_questions_id_seq',
      'test_attempts_id_seq',
      'test_attempt_answers_id_seq',
      'rate_limits_id_seq',
      'abuse_logs_id_seq'
    ];

    for (const seqName of sequences) {
      const result = await db.execute(sql.raw(`SELECT last_value FROM ${seqName};`));
      console.log(`  ${seqName}: ${result.rows[0].last_value}`);
    }

  } catch (error) {
    console.error('❌ Error fixing sequences:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the fix
fixSequences().catch(console.error);
