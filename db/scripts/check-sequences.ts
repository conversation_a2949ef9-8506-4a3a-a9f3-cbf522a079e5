#!/usr/bin/env tsx

/**
 * Check PostgreSQL sequences status
 * This script shows current sequence values vs actual max IDs in tables
 */

import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { sql } from 'drizzle-orm';

// Get database URL from environment
const databaseUrl = process.env.DATABASE_URL_PROD || process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ DATABASE_URL_PROD or DATABASE_URL environment variable is required');
  process.exit(1);
}

const pool = new Pool({
  connectionString: databaseUrl,
  ssl: databaseUrl.includes('localhost') ? false : { rejectUnauthorized: false }
});

const db = drizzle(pool);

async function checkSequences() {
  console.log('🔍 Checking PostgreSQL sequences status...\n');

  try {
    const tables = [
      { table: 'users', sequence: 'users_id_seq' },
      { table: 'questions', sequence: 'questions_id_seq' },
      { table: 'tests', sequence: 'tests_id_seq' },
      { table: 'test_questions', sequence: 'test_questions_id_seq' },
      { table: 'test_attempts', sequence: 'test_attempts_id_seq' },
      { table: 'test_attempt_answers', sequence: 'test_attempt_answers_id_seq' },
      { table: 'rate_limits', sequence: 'rate_limits_id_seq' },
      { table: 'abuse_logs', sequence: 'abuse_logs_id_seq' }
    ];

    for (const { table, sequence } of tables) {
      // Get current sequence value
      const seqResult = await db.execute(sql.raw(`SELECT last_value FROM ${sequence};`));
      const sequenceValue = seqResult.rows[0].last_value;

      // Get max ID from table
      const maxResult = await db.execute(sql.raw(`SELECT COALESCE(MAX(id), 0) as max_id FROM ${table};`));
      const maxId = maxResult.rows[0].max_id;

      // Check if they match
      const status = sequenceValue >= maxId ? '✅' : '❌';
      const problem = sequenceValue < maxId ? ' (SEQUENCE TOO LOW!)' : '';

      console.log(`${status} ${table}:`);
      console.log(`    Sequence: ${sequenceValue}`);
      console.log(`    Max ID:   ${maxId}${problem}`);
      console.log('');
    }

  } catch (error) {
    console.error('❌ Error checking sequences:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the check
checkSequences().catch(console.error);
